name: Publish
on:
  release:
    types: [published]
jobs:
  publish-npm:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write # Needed for npm provenance
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          registry-url: https://registry.npmjs.org/
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run build
      - run: npm run lint
      - run: npm run ctest
      - run: npm publish --provenance
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-docker:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write # Needed for OIDC login to Azure
    environment: allow-publishing-docker-to-acr
    steps:
      - uses: actions/checkout@v4
      - name: Set up QEMU # Needed for multi-platform builds (e.g., arm64 on amd64 runner)
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx # Needed for multi-platform builds
        uses: docker/setup-buildx-action@v3
      - name: Azure Login via OIDC
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZURE_DOCKER_CLIENT_ID }}
          tenant-id: ${{ secrets.AZURE_DOCKER_TENANT_ID }}
          subscription-id: ${{ secrets.AZURE_DOCKER_SUBSCRIPTION_ID }}
      - name: Login to ACR
        run: az acr login --name playwright
      - name: Build and push Docker image
        id: build-push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile # Adjust path if your Dockerfile is elsewhere
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            playwright.azurecr.io/public/playwright/mcp:${{ github.event.release.tag_name }}
            playwright.azurecr.io/public/playwright/mcp:latest
      - uses: oras-project/setup-oras@v1
      - name: Set oras tags
        run: |
          attach_eol_manifest() {
            local image="$1"
            local today=$(date -u +'%Y-%m-%d')
            # oras is re-using Docker credentials, so we don't need to login.
            # Following the advice in https://portal.microsofticm.com/imp/v3/incidents/incident/476783820/summary
            oras attach --artifact-type application/vnd.microsoft.artifact.lifecycle --annotation "vnd.microsoft.artifact.lifecycle.end-of-life.date=$today" $image
          }
          # for each tag, attach the eol manifest
          for tag in $(echo ${{ steps.build-push.outputs.metadata['image.name'] }} | tr ',' '\n'); do
            attach_eol_manifest $tag
          done
