# Playwright MCP 集成完整改动记录

## 概述
本文档记录了将 playwright-mcp 源码集成到 OpenManus 项目中的所有改动，实现了通过 stdio 方式在项目启动时一起启动并连接 Playwright MCP 服务。

## 1. 源码集成

### 1.1 下载并集成 playwright-mcp 源码
- 将 playwright-mcp 源码放置到 `external/playwright-mcp/` 目录
- 编译 TypeScript 源码生成 `lib/` 目录：
  ```bash
  cd external/playwright-mcp
  npm install
  npm run build
  ```

### 1.2 文件结构
```
external/playwright-mcp/
├── cli.js                 # 入口文件
├── lib/                   # 编译后的 JavaScript 文件
├── src/                   # TypeScript 源码
├── package.json
└── tsconfig.json
```

## 2. 配置文件修改

### 2.1 更新 MCP 配置 (config/mcp.json)
```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
      ]
    }
  }
}
```

### 2.2 创建 Playwright 配置文件 (playwright-mcp-config.json)
```json
{
  "browser": {
    "browserName": "chromium",
    "launchOptions": {
      "channel": "chrome",
      "headless": false,
      "chromiumSandbox": false,
      "args": [
        "--no-sandbox",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor"
      ]
    },
    "contextOptions": {
      "viewport": {
        "width": 1280,
        "height": 720
      }
    }
  }
}
```

## 3. 代码清理和修复

### 3.1 删除错误的 PlaywrightTool 类
删除了 `app/tool/playwright_tool.py` 文件，因为：
- 该文件试图重新实现 Playwright 功能
- 与 MCP 集成方式冲突
- 包含错误的工具注册逻辑

### 3.2 修复工具注册 (app/tool/__init__.py)
**原始代码：**
```python
from app.tool.playwright_tool import PlaywrightTool

# 注册所有工具
all_tools = [
    # ... 其他工具
    PlaywrightTool(),
]
```

**修改后：**
```python
# 移除了 PlaywrightTool 的导入和注册
# MCP 工具通过 MCPClients 自动注册

all_tools = [
    # ... 其他工具（不包含 PlaywrightTool）
]
```

### 3.3 改进错误处理 (app/tool/mcp.py)
**原始代码：**
```python
except Exception as e:
    return ToolResult(error=f"Error executing tool: {str(e)}")
```

**修改后：**
```python
except Exception as e:
    error_msg = (
        str(e) if str(e) else f"Unknown error of type {type(e).__name__}"
    )
    logger.error(
        f"Error executing tool {self.original_name}: {error_msg}", exc_info=True
    )
    return ToolResult(error=f"Error executing tool: {error_msg}")
```

## 4. 启动脚本创建

### 4.1 创建集成启动脚本 (run_with_playwright_mcp.py)
```python
#!/usr/bin/env python
"""
OpenManus 与 Playwright MCP 源码集成启动脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger


async def main():
    """主函数"""
    try:
        # 创建 Manus 实例
        agent = await Manus.create()

        # 手动连接到 Playwright MCP 服务
        command = "node"
        args = [
            "external/playwright-mcp/cli.js",
            "--browser=chrome",
            "--no-sandbox",
            "--config=playwright-mcp-config.json",
        ]
        server_id = "playwright"

        logger.info(f"正在连接到 Playwright MCP 服务...")

        await agent.connect_mcp_server(
            command, server_id, use_stdio=True, stdio_args=args
        )

        # 检查连接状态
        available_tools = list(agent.mcp_clients.tool_map.keys())
        playwright_tools = [
            tool for tool in available_tools if "playwright" in tool.lower()
        ]

        if playwright_tools:
            print("\n🎉 OpenManus 与 Playwright MCP 源码集成启动成功！")
            print("📝 可用的 Playwright 工具包括:")
            print("   - browser_navigate: 导航到网页")
            print("   - browser_take_screenshot: 截图")
            print("   - browser_click: 点击元素")
            print("   - browser_type: 输入文本")
            print("   - 以及更多...")
            print("\n💡 示例使用:")
            print("   '请使用 Playwright 打开 https://www.example.com 并截图'")
            print("   '请使用 Playwright 在百度搜索 OpenManus'")
            print("\n⚠️  重要提示:")
            print("   - 浏览器配置为有头模式，应该能看到浏览器窗口")
            print("   - 如果没有看到浏览器窗口，请检查任务栏或其他显示器")
            print("   - 截图会保存到临时目录，可以通过日志查看路径")
            print("\n输入 'exit' 退出")

            # 交互式循环
            while True:
                try:
                    prompt = input("\n🤖 请输入您的指令: ")

                    if prompt.lower() in ["exit", "quit", "退出"]:
                        break

                    if not prompt.strip():
                        continue

                    logger.info("正在处理您的请求...")
                    response = await agent.run(prompt)
                    logger.info("请求处理完成")

                except KeyboardInterrupt:
                    break
        else:
            logger.error("❌ 未能连接到 Playwright MCP 服务")

    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"运行时出错: {str(e)}", exc_info=True)
    finally:
        # 确保资源被清理
        if "agent" in locals():
            await agent.cleanup()

    print("\n👋 再见！")


if __name__ == "__main__":
    asyncio.run(main())
```

## 5. 环境配置

### 5.1 Conda 环境依赖
确保在正确的 conda 环境中安装依赖：
```bash
conda activate open_manus
pip install -r requirements.txt
```

### 5.2 Playwright 浏览器安装
```bash
cd external/playwright-mcp
npx playwright install
```

## 6. 关键技术要点

### 6.1 MCP 协议集成
- 使用 stdio 传输方式连接 Playwright MCP 服务
- 通过 MCPClients 类管理连接和工具
- 工具自动注册为 `mcp_playwright_*` 前缀

### 6.2 浏览器配置
- 配置为有头模式 (`headless: false`)
- 使用 Chrome 浏览器 (`channel: chrome`)
- 禁用沙箱以提高兼容性 (`--no-sandbox`)

### 6.3 错误处理改进
- 增强了 MCP 工具执行的错误处理
- 添加了详细的日志记录
- 提供了更好的错误信息

## 7. 验证结果

### 7.1 成功指标
- ✅ 成功连接到 Playwright MCP 服务
- ✅ 发现 25 个 Playwright 工具
- ✅ 浏览器导航功能正常
- ✅ 截图功能正常
- ✅ Agent 自然语言交互正常

### 7.2 可用工具列表
```
mcp_playwright_browser_navigate
mcp_playwright_browser_take_screenshot
mcp_playwright_browser_click
mcp_playwright_browser_type
mcp_playwright_browser_tab_new
mcp_playwright_browser_tab_close
... 等 25 个工具
```

## 8. 使用方法

### 8.1 启动命令
```bash
conda activate open_manus
python run_with_playwright_mcp.py
```

### 8.2 示例指令
```
请使用 Playwright 打开 https://www.example.com 并截图
请使用 Playwright 在百度搜索 OpenManus
请使用 Playwright 填写表单并提交
```

## 9. 文件清单

### 9.1 新增文件
- `external/playwright-mcp/` (整个目录)
- `playwright-mcp-config.json`
- `run_with_playwright_mcp.py`
- `PLAYWRIGHT_MCP_INTEGRATION.md`

### 9.2 修改文件
- `config/mcp.json`
- `app/tool/mcp.py`
- `app/tool/__init__.py`

### 9.3 删除文件
- `app/tool/playwright_tool.py`

## 10. 详细代码改动

### 10.1 config/mcp.json 完整内容
```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
      ]
    }
  }
}
```

### 10.2 app/tool/mcp.py 关键改动
在 `execute` 方法的异常处理部分：
```python
# 原始代码
except Exception as e:
    return ToolResult(error=f"Error executing tool: {str(e)}")

# 修改后代码
except Exception as e:
    error_msg = (
        str(e) if str(e) else f"Unknown error of type {type(e).__name__}"
    )
    logger.error(
        f"Error executing tool {self.original_name}: {error_msg}", exc_info=True
    )
    return ToolResult(error=f"Error executing tool: {error_msg}")
```

### 10.3 app/tool/__init__.py 改动
删除了以下内容：
```python
# 删除的导入
from app.tool.playwright_tool import PlaywrightTool

# 删除的工具注册
PlaywrightTool(),  # 从 all_tools 列表中移除
```

### 10.4 删除的文件内容记录
`app/tool/playwright_tool.py` 文件被完全删除，该文件包含了错误的 Playwright 实现。

## 11. 关键学习要点

### 11.1 MCP 协议理解
- MCP (Model Context Protocol) 是连接 AI 模型和外部工具的标准协议
- 支持 stdio 和 SSE 两种传输方式
- 工具通过 JSON-RPC 2.0 协议进行通信

### 11.2 集成策略
- 使用源码集成而非包依赖，确保版本控制和定制化
- 通过配置文件管理 MCP 服务器连接
- 自动工具发现和注册机制

### 11.3 错误处理最佳实践
- 详细的错误日志记录
- 优雅的异常处理
- 用户友好的错误信息

### 11.4 环境管理
- Conda 环境隔离的重要性
- 依赖包版本一致性
- 跨平台兼容性考虑

## 12. 总结

通过以上改动，成功实现了：
1. Playwright MCP 源码的完整集成
2. 通过 stdio 方式的自动连接
3. 25 个 Playwright 工具的可用性
4. 自然语言控制浏览器自动化
5. 在 conda 环境中的稳定运行

整个集成过程遵循了 MCP 协议标准，确保了与 OpenManus 项目的完美兼容。

**最终验证结果**：
- ✅ Agent 成功调用 Playwright 工具
- ✅ 浏览器自动化功能正常
- ✅ 截图和导航功能验证通过
- ✅ 错误处理机制完善
- ✅ 在 conda 环境中稳定运行
