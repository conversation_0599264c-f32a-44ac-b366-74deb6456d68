#!/usr/bin/env python
"""
设置 Playwright MCP 源码
"""
import os
import sys
import subprocess
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).resolve().parent.parent
playwright_mcp_dir = project_root / "external" / "playwright-mcp"


def run_command(cmd, cwd=None):
    """运行命令并打印输出"""
    print(f"执行命令: {' '.join(cmd)}")
    process = subprocess.run(
        cmd,
        cwd=cwd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    if process.stdout:
        print(process.stdout)
    
    if process.stderr:
        print(process.stderr, file=sys.stderr)
        
    return process.returncode


def main():
    """主函数"""
    # 创建 external 目录
    external_dir = project_root / "external"
    external_dir.mkdir(exist_ok=True)
    
    # 检查 Playwright MCP 目录是否已存在
    if playwright_mcp_dir.exists():
        print(f"Playwright MCP 目录已存在: {playwright_mcp_dir}")
        
        # 更新源码
        print("正在更新 Playwright MCP 源码...")
        if run_command(["git", "pull"], cwd=str(playwright_mcp_dir)) != 0:
            print("更新 Playwright MCP 源码失败")
            return 1
    else:
        # 克隆 Playwright MCP 仓库
        print("正在克隆 Playwright MCP 仓库...")
        if run_command([
            "git", "clone", 
            "https://github.com/microsoft/playwright-mcp.git", 
            str(playwright_mcp_dir)
        ]) != 0:
            print("克隆 Playwright MCP 仓库失败")
            return 1
    
    # 安装依赖
    print("正在安装 Playwright MCP 依赖...")
    if run_command(["npm", "install"], cwd=str(playwright_mcp_dir)) != 0:
        print("安装 Playwright MCP 依赖失败")
        return 1
    
    # 编译源码
    print("正在编译 Playwright MCP 源码...")
    if run_command(["npm", "run", "build"], cwd=str(playwright_mcp_dir)) != 0:
        print("编译 Playwright MCP 源码失败")
        return 1
    
    # 安装 Playwright 浏览器
    print("正在安装 Playwright 浏览器...")
    if run_command(["npx", "playwright", "install"], cwd=str(playwright_mcp_dir)) != 0:
        print("安装 Playwright 浏览器失败")
        return 1
    
    print("Playwright MCP 源码设置完成！")
    return 0


if __name__ == "__main__":
    sys.exit(main())