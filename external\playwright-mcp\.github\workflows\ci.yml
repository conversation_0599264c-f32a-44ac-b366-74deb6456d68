name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js 18
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    - name: Install dependencies
      run: npm ci
    - run: npm run build
    - name: Run ESLint
      run: npm run lint
    - name: Ensure no changes
      run: git diff --exit-code

  test:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js 18
      uses: actions/setup-node@v4
      with:
        # https://github.com/microsoft/playwright-mcp/issues/344
        node-version: '18.19'
        cache: 'npm'
    - name: Install dependencies
      run: npm ci
    - name: Playwright install
      run: npx playwright install --with-deps
    - name: Install MS Edge
      # MS Edge is not preinstalled on macOS runners.
      if: ${{ matrix.os == 'macos-latest' }}
      run: npx playwright install msedge
    - name: Build
      run: npm run build
    - name: Build Chrome extension
      run: npm run build:extension
    - name: Run tests
      run: npm test

  test_docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js 18
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    - name: Install dependencies
      run: npm ci
    - name: Playwright install
      run: npx playwright install --with-deps chromium
    - name: Build
      run: npm run build
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Build and push
      uses: docker/build-push-action@v6
      with:
        tags: playwright-mcp-dev:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
        load: true
    - name: Run tests
      shell: bash
      run: |
        # Used for the Docker tests to share the test-results folder with the container.
        umask 0000
        npm run test -- --project=chromium-docker
      env:
        MCP_IN_DOCKER: 1
