# Playwright MCP 集成代码示例

## 1. 配置文件

### config/mcp.json
```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
      ]
    }
  }
}
```

### playwright-mcp-config.json
```json
{
  "browser": {
    "browserName": "chromium",
    "launchOptions": {
      "channel": "chrome",
      "headless": false,
      "chromiumSandbox": false,
      "args": [
        "--no-sandbox",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor"
      ]
    },
    "contextOptions": {
      "viewport": {
        "width": 1280,
        "height": 720
      }
    }
  }
}
```

## 2. 核心代码修改

### app/tool/mcp.py 错误处理改进
```python
async def execute(self, **kwargs) -> ToolResult:
    """执行 MCP 工具"""
    try:
        logger.info(f"Executing tool: {self.original_name}")
        result = await self.session.call_tool(self.original_name, kwargs)
        content_str = ", ".join(
            item.text for item in result.content if isinstance(item, TextContent)
        )
        return ToolResult(output=content_str or "No output returned.")
    except Exception as e:
        error_msg = (
            str(e) if str(e) else f"Unknown error of type {type(e).__name__}"
        )
        logger.error(
            f"Error executing tool {self.original_name}: {error_msg}", exc_info=True
        )
        return ToolResult(error=f"Error executing tool: {error_msg}")
```

### app/tool/__init__.py 工具注册清理
```python
# 移除了错误的 PlaywrightTool 导入和注册
# from app.tool.playwright_tool import PlaywrightTool  # 删除此行

# 在 all_tools 列表中移除了 PlaywrightTool()

all_tools = [
    # ... 其他工具
    # PlaywrightTool(),  # 删除此行
]
```

## 3. 启动脚本

### run_with_playwright_mcp.py (第1部分)
```python
#!/usr/bin/env python
"""
OpenManus 与 Playwright MCP 源码集成启动脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger


async def main():
    """主函数"""
    try:
        # 创建 Manus 实例
        agent = await Manus.create()

        # 手动连接到 Playwright MCP 服务
        command = "node"
        args = [
            "external/playwright-mcp/cli.js",
            "--browser=chrome",
            "--no-sandbox",
            "--config=playwright-mcp-config.json",
        ]
        server_id = "playwright"

        logger.info(f"正在连接到 Playwright MCP 服务...")

        await agent.connect_mcp_server(
            command, server_id, use_stdio=True, stdio_args=args
        )

        # 检查连接状态
        available_tools = list(agent.mcp_clients.tool_map.keys())
        playwright_tools = [
            tool for tool in available_tools if "playwright" in tool.lower()
        ]

        if playwright_tools:
            print("\n🎉 OpenManus 与 Playwright MCP 源码集成启动成功！")
            print("📝 可用的 Playwright 工具包括:")
            print("   - browser_navigate: 导航到网页")
            print("   - browser_take_screenshot: 截图")
            print("   - browser_click: 点击元素")
            print("   - browser_type: 输入文本")
            print("   - 以及更多...")
            print("\n💡 示例使用:")
            print("   '请使用 Playwright 打开 https://www.example.com 并截图'")
            print("   '请使用 Playwright 在百度搜索 OpenManus'")
            print("\n⚠️  重要提示:")
            print("   - 浏览器配置为有头模式，应该能看到浏览器窗口")
            print("   - 如果没有看到浏览器窗口，请检查任务栏或其他显示器")
            print("   - 截图会保存到临时目录，可以通过日志查看路径")
            print("\n输入 'exit' 退出")

            # 交互式循环
            while True:
                try:
                    prompt = input("\n🤖 请输入您的指令: ")

                    if prompt.lower() in ["exit", "quit", "退出"]:
                        break

                    if not prompt.strip():
                        continue

                    logger.info("正在处理您的请求...")
                    response = await agent.run(prompt)
                    logger.info("请求处理完成")

                except KeyboardInterrupt:
                    break
        else:
            logger.error("❌ 未能连接到 Playwright MCP 服务")

    except KeyboardInterrupt:
        logger.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"运行时出错: {str(e)}", exc_info=True)
    finally:
        # 确保资源被清理
        if "agent" in locals():
            await agent.cleanup()

    print("\n👋 再见！")


if __name__ == "__main__":
    asyncio.run(main())
```

## 4. 测试验证代码

### 直接 MCP 测试示例
```python
import asyncio
import subprocess
import json

async def test_playwright_mcp():
    """直接测试 Playwright MCP 服务"""
    cmd = [
        "node",
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
    ]

    process = subprocess.Popen(
        cmd,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    # 初始化请求
    init_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {},
            "clientInfo": {"name": "test-client", "version": "1.0.0"}
        }
    }

    process.stdin.write(json.dumps(init_request) + "\n")
    process.stdin.flush()

    # 导航请求
    navigate_request = {
        "jsonrpc": "2.0",
        "id": 3,
        "method": "tools/call",
        "params": {
            "name": "browser_navigate",
            "arguments": {"url": "https://www.example.com"}
        }
    }

    process.stdin.write(json.dumps(navigate_request) + "\n")
    process.stdin.flush()

    # 读取响应
    response = process.stdout.readline()
    print(f"导航响应: {response}")

    process.terminate()
```

## 5. 环境配置命令

### Conda 环境设置
```bash
# 激活环境
conda activate open_manus

# 安装依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
cd external/playwright-mcp
npx playwright install
```

### 编译 TypeScript 源码
```bash
cd external/playwright-mcp
npm install
npm run build
```

## 6. 使用示例

### 启动命令
```bash
conda activate open_manus
python run_with_playwright_mcp.py
```

### 交互示例
```
🤖 请输入您的指令: 请使用 Playwright 打开 https://www.example.com 并截图

# Agent 会自动：
# 1. 调用 browser_navigate 工具导航到网页
# 2. 调用 browser_take_screenshot 工具截图
# 3. 返回操作结果
```

## 7. 关键技术点

### MCP 工具前缀
所有 Playwright 工具都会自动添加 `mcp_playwright_` 前缀：
- `browser_navigate` → `mcp_playwright_browser_navigate`
- `browser_take_screenshot` → `mcp_playwright_browser_take_screenshot`
- `browser_click` → `mcp_playwright_browser_click`

### 错误处理机制
```python
# 改进的错误处理确保：
# 1. 详细的错误日志记录
# 2. 用户友好的错误信息
# 3. 异常类型识别
# 4. 堆栈跟踪记录
```

### 浏览器配置要点
```json
{
  "headless": false,           // 有头模式，显示浏览器窗口
  "chromiumSandbox": false,    // 禁用沙箱，提高兼容性
  "channel": "chrome",         // 使用 Chrome 浏览器
  "args": ["--no-sandbox"]     // 额外的启动参数
}
```

这些代码示例展示了完整的 Playwright MCP 集成实现过程。
