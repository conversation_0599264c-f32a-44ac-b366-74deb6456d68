# Playwright MCP 集成说明

## 🎉 集成完成

Playwright MCP 已成功集成到 OpenManus 项目中，现在可以通过 stdio 方式在项目启动时一起启动并使用。

## 📁 文件结构

```
OpenManus-main/
├── config/
│   └── mcp.json                    # MCP 服务器配置
├── external/
│   └── playwright-mcp/             # Playwright MCP 源码
│       ├── cli.js                  # 入口文件
│       ├── lib/                    # 编译后的 JavaScript 文件
│       └── src/                    # TypeScript 源码
├── playwright-mcp-config.json      # Playwright 配置文件（有头模式）
└── run_with_playwright_mcp.py      # 启动脚本
```

## 🚀 使用方法

### 1. 启动集成环境

```bash
python run_with_playwright_mcp.py
```

### 2. 可用的 Playwright 工具

集成后，您将拥有 25 个 Playwright 工具，包括：

- `browser_navigate` - 导航到网页
- `browser_take_screenshot` - 截图
- `browser_click` - 点击元素
- `browser_type` - 输入文本
- `browser_tab_new` - 创建新标签页
- `browser_tab_close` - 关闭标签页
- `browser_wait_for` - 等待元素
- 以及更多浏览器自动化功能...

### 3. 使用示例

您可以使用自然语言指令来控制 Playwright：

```
请使用 Playwright 打开 https://www.example.com 并截图
请使用 Playwright 在百度搜索 OpenManus
请使用 Playwright 填写表单并提交
请使用 Playwright 点击页面上的登录按钮
```

## ⚙️ 配置说明

### MCP 配置 (config/mcp.json)

```json
{
  "mcpServers": {
    "playwright": {
      "type": "stdio",
      "command": "node",
      "args": [
        "external/playwright-mcp/cli.js",
        "--browser=chrome",
        "--no-sandbox",
        "--config=playwright-mcp-config.json"
      ]
    }
  }
}
```

### Playwright 配置 (playwright-mcp-config.json)

```json
{
  "browser": {
    "browserName": "chromium",
    "launchOptions": {
      "channel": "chrome",
      "headless": false,
      "chromiumSandbox": false
    },
    "contextOptions": {
      "viewport": {
        "width": 1280,
        "height": 720
      }
    }
  }
}
```

## 🔧 浏览器可见性

- **有头模式**: 配置为 `"headless": false`，浏览器窗口应该可见
- **浏览器类型**: 使用 Chrome 浏览器 (`--browser=chrome`)
- **安全设置**: 禁用沙箱 (`--no-sandbox`) 以确保兼容性

## 📝 注意事项

1. **浏览器安装**: 确保已安装 Playwright 浏览器：
   ```bash
   cd external/playwright-mcp
   npx playwright install
   ```

2. **权限问题**: 如果遇到权限问题，可能需要以管理员身份运行

3. **防火墙**: 确保防火墙允许 Node.js 和 Chrome 的网络访问

4. **性能**: 有头模式会消耗更多资源，如需要更好的性能可以切换到 headless 模式

## 🐛 故障排除

### 浏览器不显示

1. 检查配置文件中的 `"headless": false` 设置
2. 确认 Chrome 浏览器已正确安装
3. 尝试手动运行：
   ```bash
   node external/playwright-mcp/cli.js --browser=chrome --config=playwright-mcp-config.json
   ```

### 连接失败

1. 检查 Node.js 是否已安装并在 PATH 中
2. 确认 playwright-mcp 源码已正确编译
3. 查看日志输出中的错误信息

## 🎯 技术细节

- **协议**: Model Context Protocol (MCP)
- **传输方式**: stdio
- **工具前缀**: `mcp_playwright_`
- **浏览器引擎**: Chromium (Chrome)
- **配置文件**: JSON 格式

现在您可以享受完整的 Playwright 浏览器自动化功能了！🎉
